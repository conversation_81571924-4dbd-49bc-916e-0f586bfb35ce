import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text, Button } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faCreditCard } from "@fortawesome/free-regular-svg-icons"
import { colors, spacing, typography } from "app/theme"

export interface SubscriptionEmptyStateProps {
  /**
   * Callback when the add subscription button is pressed
   */
  onAddSubscription: () => void
  /**
   * Optional style override for the container
   */
  style?: ViewStyle
}

/**
 * Empty state component specifically designed for subscription screens.
 * Shows when there are no subscriptions to display.
 */
export const SubscriptionEmptyState = observer(function SubscriptionEmptyState(
  props: SubscriptionEmptyStateProps
) {
  const { onAddSubscription, style } = props

  return (
    <View style={[$container, style]}>
      <View style={$iconContainer}>
        <FontAwesomeIcon
          icon={faCreditCard}
          color={colors.textDim}
          size={64}
        />
      </View>
      
      <Text style={$heading}>
        Add your first subscription
      </Text>
      
      <Text style={$message}>
        Start tracking your subscriptions to stay on top of your recurring expenses
      </Text>
      
      <Button
        text="Add Subscription"
        preset="reversed"
        style={$button}
        onPress={onAddSubscription}
      />
    </View>
  )
})

const $container: ViewStyle = {
  flex: 1,
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.xxxl,
}

const $iconContainer: ViewStyle = {
  marginBottom: spacing.lg,
}

const $heading: TextStyle = {
  fontSize: 24,
  fontFamily: typography.primary.bold,
  color: colors.text,
  textAlign: 'center',
  marginBottom: spacing.md,
}

const $message: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  textAlign: 'center',
  lineHeight: 24,
  marginBottom: spacing.xl,
  maxWidth: 280,
}

const $button: ViewStyle = {
  paddingHorizontal: spacing.lg,
  minWidth: 160,
}
